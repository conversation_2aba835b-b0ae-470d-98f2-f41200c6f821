<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>滚动条修复测试 - Timing Belt Solutions</title>
  <link rel="stylesheet" href="assets/css/style.css">
  <link rel="stylesheet" href="assets/css/contact-box.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="main-navbar">
    <div class="navbar-left">
      <a href="/" class="navbar-logo">
        <span class="logo-icon">⚙️</span>
        <span class="logo-text">TIMING BELT SOLUTIONS</span>
      </a>
    </div>
    
    <div class="navbar-center">
      <a href="/products/" class="nav-link">Products</a>
      <div class="nav-dropdown nav-industries">
        <a href="/industries/" class="nav-link">Industries</a>
        <div class="nav-submenu">
          <div class="submenu-header">Our Industries</div>
          <div class="industries-grid">
            <div class="industry-col">
              <a href="/industries/energy/">Energy & Power</a>
              <a href="/industries/automotive/">Automotive</a>
              <a href="/industries/packaging/">Packaging</a>
            </div>
            <div class="industry-col">
              <a href="/industries/manufacturing/">Manufacturing</a>
              <a href="/industries/food/">Food Processing</a>
              <a href="/industries/medical/">Medical</a>
            </div>
          </div>
        </div>
      </div>
      <a href="/resources/" class="nav-link">Resources</a>
    </div>
    
    <div class="navbar-right">
      <a href="#" class="btn btn-primary contact-box-trigger">Contact Us</a>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main>
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <div class="hero-text">
            <h1>滚动条修复测试页面</h1>
            <p>这个页面用于测试滚动条异常滑动的修复效果。请尝试以下操作：</p>
            <ul>
              <li>滚动页面，观察滚动条是否正常</li>
              <li>点击"Contact Us"按钮打开弹窗</li>
              <li>在弹窗打开状态下，页面应该不能滚动</li>
              <li>关闭弹窗后，页面应该恢复到之前的滚动位置</li>
            </ul>
            <div class="hero-cta-buttons">
              <a href="#" class="btn btn-primary btn-large contact-box-trigger">测试联系弹窗</a>
              <a href="#content" class="btn btn-outline btn-large">滚动到内容</a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 测试内容区域 -->
    <section id="content" class="test-content">
      <div class="container">
        <h2>测试内容区域</h2>
        <p>这里是一些测试内容，用于创建足够的页面高度来测试滚动功能。</p>
        
        <div class="test-sections">
          <div class="test-section">
            <h3>第一部分</h3>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
          </div>
          
          <div class="test-section">
            <h3>第二部分</h3>
            <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
            <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
          </div>
          
          <div class="test-section">
            <h3>第三部分</h3>
            <p>At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.</p>
            <p>Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio.</p>
          </div>
          
          <div class="test-section">
            <h3>第四部分</h3>
            <p>Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.</p>
            <p>Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae.</p>
          </div>
          
          <div class="test-section">
            <h3>第五部分</h3>
            <p>Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.</p>
            <p>Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur?</p>
          </div>
          
          <div class="test-section">
            <h3>第六部分</h3>
            <p>But I must explain to you how all this mistaken idea of denouncing pleasure and praising pain was born and I will give you a complete account of the system.</p>
            <p>And expound the actual teachings of the great explorer of the truth, the master-builder of human happiness.</p>
          </div>
          
          <div class="test-section">
            <h3>第七部分</h3>
            <p>No one rejects, dislikes, or avoids pleasure itself, because it is pleasure, but because those who do not know how to pursue pleasure rationally encounter consequences that are extremely painful.</p>
            <p>Nor again is there anyone who loves or pursues or desires to obtain pain of itself, because it is pain, but because occasionally circumstances occur.</p>
          </div>
          
          <div class="test-section">
            <h3>第八部分</h3>
            <p>On the other hand, we denounce with righteous indignation and dislike men who are so beguiled and demoralized by the charms of pleasure of the moment.</p>
            <p>So blinded by desire, that they cannot foresee the pain and trouble that are bound to ensue; and equal blame belongs to those who fail in their duty.</p>
          </div>
          
          <div class="test-section">
            <h3>第九部分</h3>
            <p>These cases are perfectly simple and easy to distinguish. In a free hour, when our power of choice is untrammelled and when nothing prevents our being able to do what we like best.</p>
            <p>Every pleasure is to be welcomed and every pain avoided. But in certain circumstances and owing to the claims of duty or the obligations of business.</p>
          </div>
          
          <div class="test-section">
            <h3>第十部分</h3>
            <p>It will frequently occur that pleasures have to be repudiated and annoyances accepted. The wise man therefore always holds in these matters to this principle of selection.</p>
            <p>He rejects pleasures to secure other greater pleasures, or else he endures pains to avoid worse pains.</p>
            <a href="#" class="btn btn-primary contact-box-trigger">再次测试联系弹窗</a>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- 右下角联系框 -->
  <div class="contact-box-trigger contact-box-fixed">
    <span>Contact Us</span>
    <i class="dashicons dashicons-arrow-right-alt">→</i>
  </div>

  <!-- 联系表单弹窗 -->
  <div class="contact-modal">
    <div class="contact-form-container">
      <div class="contact-form-close">&times;</div>
      <div class="contact-form-header">
        <h2>Contact Us</h2>
        <p>测试联系表单弹窗</p>
      </div>
      <form id="contact-form" class="contact-form">
        <div class="form-group">
          <input type="text" name="name" placeholder="Your Name" required>
        </div>
        <div class="form-group">
          <input type="email" name="email" placeholder="Your Email" required>
        </div>
        <div class="form-group">
          <input type="tel" name="phone" placeholder="Phone Number">
        </div>
        <div class="form-group">
          <select name="subject" required>
            <option value="">Select Subject</option>
            <option value="scrollbar-test">滚动条测试</option>
            <option value="technical-support">Technical Support</option>
            <option value="other">Other</option>
          </select>
        </div>
        <div class="form-group">
          <textarea name="message" placeholder="Your Message" rows="5" required></textarea>
        </div>
        <button type="submit" class="btn-submit">Send Message</button>
      </form>
    </div>
  </div>

  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="assets/js/navbar.js"></script>
  <script src="assets/js/contact-box.js"></script>
  
  <style>
    .test-content {
      padding: 80px 0;
      background: #f8f9fa;
    }
    
    .test-sections {
      margin-top: 40px;
    }
    
    .test-section {
      background: white;
      padding: 30px;
      margin-bottom: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }
    
    .test-section h3 {
      color: #1A365D;
      margin-bottom: 20px;
    }
    
    .test-section p {
      margin-bottom: 15px;
      line-height: 1.6;
    }
  </style>
</body>
</html>
