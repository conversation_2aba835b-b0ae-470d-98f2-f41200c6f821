/**
 * {{ AURA-X: Modify - 现代图片懒加载实现，增强性能优化 }}
 * 使用Intersection Observer API，支持WebP格式和响应式图片
 * Source: 基于WP Rocket和现代Web性能最佳实践
 */

class LazyImageLoader {
    constructor() {
        this.imageObserver = null;
        this.videoObserver = null;
        this.webpSupported = null;
        this.init();
    }

    init() {
        // 检查浏览器支持
        if ('IntersectionObserver' in window) {
            // 图片观察器
            this.imageObserver = new IntersectionObserver(this.onIntersection.bind(this), {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // 视频观察器
            this.videoObserver = new IntersectionObserver(this.onVideoIntersection.bind(this), {
                rootMargin: '200px 0px',
                threshold: 0.01
            });

            this.observeImages();
            this.observeVideos();
        } else {
            // 降级处理：直接加载所有图片
            this.loadAllImages();
        }

        // 检查WebP支持
        this.checkWebPSupport();
    }

    observeImages() {
        const lazyImages = document.querySelectorAll('img[data-src], img[loading="lazy"]');
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }

    observeVideos() {
        const lazyVideos = document.querySelectorAll('video[data-src]');
        lazyVideos.forEach(video => {
            this.videoObserver.observe(video);
        });
    }

    onIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                this.loadImage(img);
                this.imageObserver.unobserve(img);
            }
        });
    }

    onVideoIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const video = entry.target;
                this.loadVideo(video);
                this.videoObserver.unobserve(video);
            }
        });
    }

    loadImage(img) {
        const dataSrc = img.dataset.src;

        // 如果没有data-src，可能是原生lazy loading
        if (!dataSrc && img.hasAttribute('loading')) {
            return;
        }

        // 检查WebP支持
        if (this.webpSupported && dataSrc) {
            const webpSrc = dataSrc.replace(/\.(jpg|jpeg|png)$/i, '.webp');
            // 尝试加载WebP版本
            this.tryLoadWebP(img, webpSrc, dataSrc);
        } else if (dataSrc) {
            // 直接加载原图
            this.setImageSrc(img, dataSrc);
        }
    }

    loadVideo(video) {
        const dataSrc = video.dataset.src;
        if (dataSrc) {
            video.src = dataSrc;
            video.load();
            video.removeAttribute('data-src');
            video.classList.add('loaded');
        }
    }

    tryLoadWebP(img, webpSrc, fallbackSrc) {
        const testImg = new Image();
        testImg.onload = () => {
            this.setImageSrc(img, webpSrc);
        };
        testImg.onerror = () => {
            this.setImageSrc(img, fallbackSrc);
        };
        testImg.src = webpSrc;
    }

    setImageSrc(img, src) {
        img.src = src;
        img.classList.add('loaded');
        
        // 移除data-src属性
        img.removeAttribute('data-src');
        
        // 添加淡入动画
        img.style.opacity = '0';
        img.style.transition = 'opacity 0.3s ease';
        
        img.onload = () => {
            img.style.opacity = '1';
        };
    }

    checkWebPSupport() {
        // 异步WebP支持检测，避免阻塞
        if (this.webpSupported !== null) return;

        const webpData = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
        const img = new Image();

        img.onload = img.onerror = () => {
            this.webpSupported = (img.height === 2);
        };

        img.src = webpData;
    }

    supportsWebP() {
        return this.webpSupported === true;
    }

    loadAllImages() {
        // 降级处理：立即加载所有图片
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            this.loadImage(img);
        });
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    new LazyImageLoader();
});

// 添加CSS样式
const style = document.createElement('style');
style.textContent = `
    img[data-src] {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }
    
    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    img.loaded {
        background: none;
        animation: none;
    }
`;
document.head.appendChild(style);
