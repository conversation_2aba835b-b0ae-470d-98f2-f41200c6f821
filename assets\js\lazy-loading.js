/**
 * 现代图片懒加载实现
 * 使用Intersection Observer API，支持WebP格式
 * Source: 基于现代Web性能最佳实践
 */

class LazyImageLoader {
    constructor() {
        this.imageObserver = null;
        this.init();
    }

    init() {
        // 检查浏览器支持
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver(this.onIntersection.bind(this), {
                rootMargin: '50px 0px',
                threshold: 0.01
            });
            
            this.observeImages();
        } else {
            // 降级处理：直接加载所有图片
            this.loadAllImages();
        }
    }

    observeImages() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }

    onIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                this.loadImage(img);
                this.imageObserver.unobserve(img);
            }
        });
    }

    loadImage(img) {
        // 检查WebP支持
        if (this.supportsWebP()) {
            const webpSrc = img.dataset.src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
            // 尝试加载WebP版本
            this.tryLoadWebP(img, webpSrc, img.dataset.src);
        } else {
            // 直接加载原图
            this.setImageSrc(img, img.dataset.src);
        }
    }

    tryLoadWebP(img, webpSrc, fallbackSrc) {
        const testImg = new Image();
        testImg.onload = () => {
            this.setImageSrc(img, webpSrc);
        };
        testImg.onerror = () => {
            this.setImageSrc(img, fallbackSrc);
        };
        testImg.src = webpSrc;
    }

    setImageSrc(img, src) {
        img.src = src;
        img.classList.add('loaded');
        
        // 移除data-src属性
        img.removeAttribute('data-src');
        
        // 添加淡入动画
        img.style.opacity = '0';
        img.style.transition = 'opacity 0.3s ease';
        
        img.onload = () => {
            img.style.opacity = '1';
        };
    }

    supportsWebP() {
        // 简单的WebP支持检测
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    }

    loadAllImages() {
        // 降级处理：立即加载所有图片
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            this.loadImage(img);
        });
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    new LazyImageLoader();
});

// 添加CSS样式
const style = document.createElement('style');
style.textContent = `
    img[data-src] {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }
    
    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    img.loaded {
        background: none;
        animation: none;
    }
`;
document.head.appendChild(style);
