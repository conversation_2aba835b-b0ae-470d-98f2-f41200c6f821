/* Contact page styles */
.contact-page {
  background-color: #ffffff;
  color: #333;
}

/* Page header area */
.page-header {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
  position: relative;
}

.page-header h1 {
  font-size: 3.2rem;
  margin: 0 0 15px 0;
  font-weight: 700;
  color: white;
  position: relative;
  display: inline-block;
}

.page-header h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #ff6a00;
  border-radius: 2px;
}

.page-header .subtitle {
  font-size: 1.3rem;
  margin: 20px 0 2rem;
  color: rgba(255,255,255,0.9);
  font-weight: 300;
}

/* Contact highlights */
.contact-highlights {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 40px;
  flex-wrap: wrap;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(255,255,255,0.1);
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  transition: transform 0.3s ease;
}

.highlight-item:hover {
  transform: translateY(-2px);
}

.highlight-item .icon {
  font-size: 1.2rem;
}

/* Contact content area */
.contact-content {
  padding: 100px 0;
  background-color: #fff;
}

.contact-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 60px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Contact information part */
.contact-info {
  flex: 1 1 350px;
  background-color: #fff;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-info:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
}

.contact-info h2 {
  color: #222;
  font-size: 1.8rem;
  margin-top: 0;
  margin-bottom: 40px;
  position: relative;
  padding-bottom: 15px;
}

.contact-info h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: #ff6a00;
  border-radius: 2px;
}

.info-item {
  display: flex;
  margin-bottom: 35px;
}

.info-icon {
  flex: 0 0 56px;
  height: 56px;
  background-color: rgba(255, 106, 0, 0.08);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24px;
  transition: all 0.3s ease;
}

.info-item:hover .info-icon {
  background-color: rgba(255, 106, 0, 0.15);
  transform: scale(1.05);
}

.info-icon i {
  font-size: 24px;
  color: #ff6a00;
}

.info-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.15rem;
  color: #333;
  font-weight: 600;
}

.info-content p {
  margin: 0 0 5px;
  color: #666;
  line-height: 1.6;
  font-size: 1.05rem;
}

/* Contact form part */
.contact-form-wrapper {
  flex: 1 1 500px;
  background-color: #fff;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-form-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
}

.contact-form-wrapper h2 {
  color: #222;
  font-size: 1.8rem;
  margin-top: 0;
  margin-bottom: 40px;
  position: relative;
  padding-bottom: 15px;
}

.contact-form-wrapper h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: #ff6a00;
  border-radius: 2px;
}

.page-contact-form .form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12px 24px;
}

.page-contact-form .form-col {
  flex: 1 0 50%;
  padding: 0 12px;
}

.page-contact-form .form-group {
  margin-bottom: 24px;
}

.page-contact-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #444;
  font-size: 15px;
}

.page-contact-form input,
.page-contact-form select,
.page-contact-form textarea {
  width: 100%;
  padding: 14px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.page-contact-form input:focus,
.page-contact-form select:focus,
.page-contact-form textarea:focus {
  outline: none;
  border-color: #ff6a00;
  box-shadow: 0 0 0 3px rgba(255, 106, 0, 0.1);
}

.page-contact-form textarea {
  height: 160px;
  resize: vertical;
}

.page-contact-form .required {
  color: #ff6a00;
  margin-left: 3px;
}

.page-contact-form .gdpr-checkbox {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30px;
  padding: 18px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eee;
}

.page-contact-form .gdpr-checkbox input {
  width: auto;
  margin-right: 12px;
  margin-top: 5px;
  transform: scale(1.2);
}

.page-contact-form .gdpr-checkbox label {
  margin-bottom: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #666;
}

.page-contact-form button {
  background-color: #ff6a00;
  color: white;
  border: none;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: block;
  margin: 0 auto;
  min-width: 200px;
  box-shadow: 0 4px 10px rgba(255, 106, 0, 0.2);
  letter-spacing: 0.5px;
}

.page-contact-form button:hover {
  background-color: #e65c00;
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(255, 106, 0, 0.25);
}

.page-contact-form button:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 106, 0, 0.2);
}

/* Form response messages */
#form-response {
  margin-top: 24px;
  padding: 18px;
  border-radius: 8px;
  display: none;
  text-align: center;
  font-size: 16px;
}

#form-response.success {
  background-color: #e8f5e9;
  border: 1px solid #c8e6c9;
  color: #2e7d32;
  display: block;
}

#form-response.error {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  color: #c62828;
  display: block;
}

/* Map area */
.contact-map {
  height: 500px;
  position: relative;
  border-top: 1px solid #eaeaea;
}

.map-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .contact-content {
    padding: 70px 0;
  }
  
  .contact-grid {
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 60px 0;
  }
  
  .page-header h1 {
    font-size: 2.5rem;
  }
  
  .page-header .subtitle {
    font-size: 1.1rem;
  }
  
  .contact-content {
    padding: 50px 0;
  }
  
  .contact-info,
  .contact-form-wrapper {
    padding: 30px;
  }
  
  .page-contact-form .form-col {
    flex: 1 0 100%;
  }
  
  .contact-map {
    height: 350px;
  }
}

@media (max-width: 480px) {
  .page-header h1 {
    font-size: 2rem;
  }
  
  .info-icon {
    flex: 0 0 45px;
    height: 45px;
    margin-right: 15px;
  }
  
  .info-icon i {
    font-size: 20px;
  }
}

/* Enhanced Contact Page Styles */

/* Contact info enhancements */
.contact-info .contact-intro {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.6;
}

.contact-info .info-note {
  display: block;
  font-size: 0.85rem;
  color: #888;
  margin-top: 5px;
  font-style: italic;
}

/* Quick contact methods */
.quick-contact {
  margin-top: 40px;
  padding: 25px;
  background: #f8f9fa;
  border-radius: 12px;
}

.quick-contact h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.2rem;
}

.quick-contact-methods {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.quick-method {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  text-decoration: none;
  color: #333;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.quick-method:hover {
  background: #2a5298;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(42,82,152,0.3);
}

.quick-method .method-icon {
  font-size: 1.1rem;
}

/* Company credentials */
.company-credentials {
  margin-top: 40px;
  padding: 25px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
}

.company-credentials h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.2rem;
}

.credentials-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.credential-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: #555;
}

.credential-item .credential-icon {
  font-size: 1.1rem;
}

/* Form enhancements */
.form-intro {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.6;
}

.quick-inquiry-buttons {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.quick-btn {
  padding: 10px 20px;
  background: #f8f9fa;
  border: 2px solid #ddd;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  color: #555;
}

.quick-btn:hover,
.quick-btn.active {
  background: #2a5298;
  color: white;
  border-color: #2a5298;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #999;
  font-style: italic;
}

/* File upload styling */
.file-upload-group {
  margin: 25px 0;
}

.file-upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  background: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: #2a5298;
  background: #f0f4ff;
}

.file-upload-area input[type="file"] {
  display: none;
}

.file-upload-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.upload-icon {
  font-size: 2rem;
  color: #666;
}

.file-upload-text span {
  color: #666;
  font-size: 1.1rem;
}

.file-upload-text small {
  color: #888;
  font-size: 0.85rem;
}

/* Enhanced submit button */
.submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 200px;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(42,82,152,0.3);
}

.btn-icon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.submit-btn:hover .btn-icon {
  transform: translateX(5px);
}

/* Response guarantee */
.response-guarantee {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  flex-wrap: wrap;
}

.guarantee-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #555;
}

.guarantee-icon {
  font-size: 1.1rem;
}

/* Trade Information Section */
.trade-information {
  padding: 80px 0;
  background: #f8f9fa;
}

.trade-information h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 50px;
}

.trade-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.trade-info-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.trade-info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.trade-icon {
  text-align: center;
  margin-bottom: 20px;
}

.trade-icon span {
  font-size: 3rem;
  display: inline-block;
}

.trade-info-card h3 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.trade-info-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.trade-info-card li {
  padding: 8px 0;
  padding-left: 20px;
  position: relative;
  color: #666;
  border-bottom: 1px solid #f0f0f0;
}

.trade-info-card li:last-child {
  border-bottom: none;
}

.trade-info-card li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #2a5298;
  font-weight: bold;
}

/* Map section enhancements */
.contact-map h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 20px;
}

.map-intro {
  text-align: center;
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.map-info {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-top: 30px;
  flex-wrap: wrap;
}

.map-info-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.95rem;
  color: #666;
}

.map-info-icon {
  font-size: 1.2rem;
}

/* Responsive design for new elements */
@media (max-width: 768px) {
  .contact-highlights {
    gap: 15px;
  }

  .highlight-item {
    padding: 10px 15px;
    font-size: 0.85rem;
  }

  .quick-contact-methods {
    justify-content: center;
  }

  .credentials-list {
    grid-template-columns: 1fr;
  }

  .quick-inquiry-buttons {
    justify-content: center;
  }

  .response-guarantee {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .trade-info-grid {
    grid-template-columns: 1fr;
  }

  .map-info {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .page-header h1 {
    font-size: 2.5rem;
  }

  .contact-highlights {
    flex-direction: column;
    align-items: center;
  }

  .highlight-item {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }

  .quick-inquiry-buttons {
    flex-direction: column;
  }

  .quick-btn {
    width: 100%;
    text-align: center;
  }
}

/* Enhanced form response messages */
.success-message,
.error-message {
  text-align: center;
  padding: 30px;
  border-radius: 12px;
  margin: 20px 0;
}

.success-message {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
  color: #155724;
}

.error-message {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.success-icon,
.error-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  display: block;
}

.success-message h3,
.error-message h3 {
  margin: 0 0 15px 0;
  font-size: 1.5rem;
}

.success-message p,
.error-message p {
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.6;
}