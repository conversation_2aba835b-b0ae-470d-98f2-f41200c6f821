<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>导航栏测试</title>
  <style>
    /* 全新的导航栏样式 */
    .main-navbar {
      background: #fff;
      padding: 0 20px;
      display: flex;
      align-items: center;
      gap: 20px;
      min-height: 60px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      position: relative;
      z-index: 100;
    }

    .navbar-logo {
      font-size: 1.2rem;
      font-weight: bold;
      color: #15304b;
      margin-right: 20px;
    }

    .nav-dropdown {
      position: relative;
      display: inline-block;
    }

    .nav-link {
      color: #15304b;
      text-decoration: none;
      font-size: 1.08rem;
      font-weight: 500;
      padding: 8px 16px;
      border-radius: 4px;
      transition: all 0.2s ease;
      white-space: nowrap;
      display: block;
    }

    .nav-link:hover {
      background: #f4f6fa;
      color: #ff6a00;
    }

    .nav-submenu {
      position: absolute;
      top: 100%;
      left: 0;
      background: #ffffff;
      min-width: 200px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.12);
      border-radius: 8px;
      padding: 8px 0;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.3s ease;
      z-index: 1000;
      border: 1px solid rgba(0,0,0,0.08);
    }

    .nav-dropdown:hover .nav-submenu {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .nav-submenu a {
      display: block;
      padding: 10px 20px;
      color: #333;
      text-decoration: none;
      font-size: 0.95rem;
      transition: all 0.2s ease;
    }

    .nav-submenu a:hover {
      background: #f8f9fa;
      color: #ff6a00;
    }

    /* 行业菜单特殊样式 - 美化版 */
    .nav-industries .nav-submenu {
      min-width: 720px;
      max-width: 720px; /* 固定宽度防止抖动 */
      width: 720px; /* 强制固定宽度 */
      left: -250px;
      padding: 20px 0;
      background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
      border: 1px solid rgba(74,144,226,0.1);
      box-shadow:
        0 20px 40px rgba(0,0,0,0.15),
        0 8px 16px rgba(0,0,0,0.1),
        inset 0 1px 0 rgba(255,255,255,0.9);
      overflow: hidden; /* 防止内容溢出导致抖动 */
    }

    .submenu-header {
      padding: 12px 30px 16px;
      font-size: 0.9rem;
      font-weight: 700;
      color: #ff6a00;
      text-transform: uppercase;
      letter-spacing: 1px;
      border-bottom: 2px solid #ff6a00;
      margin-bottom: 20px;
      background: linear-gradient(90deg, rgba(255,106,0,0.05) 0%, transparent 100%);
      position: relative;
    }

    .submenu-header::after {
      content: '';
      position: absolute;
      left: 30px;
      bottom: -2px;
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, #ff6a00, #e55a00);
      border-radius: 1px;
    }

    .industries-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 0;
      padding: 0 15px;
      width: 100%; /* 确保网格占满容器 */
      box-sizing: border-box;
    }

    .industry-col {
      display: flex;
      flex-direction: column;
      padding: 0 15px;
      position: relative;
      width: 100%; /* 确保每列宽度一致 */
      box-sizing: border-box;
      min-height: 300px; /* 设置最小高度确保一致性 */
    }

    .industry-col:not(:last-child)::after {
      content: '';
      position: absolute;
      right: 0;
      top: 10px;
      bottom: 10px;
      width: 1px;
      background: linear-gradient(to bottom,
        transparent 0%,
        rgba(74,144,226,0.2) 20%,
        rgba(74,144,226,0.3) 50%,
        rgba(74,144,226,0.2) 80%,
        transparent 100%);
      pointer-events: none; /* 防止分隔线影响鼠标事件 */
    }

    .industry-col a {
      display: block;
      padding: 14px 20px;
      font-size: 0.95rem;
      font-weight: 500;
      color: #2E5C8A;
      transition: all 0.25s ease; /* 缩短动画时间减少抖动 */
      border-radius: 8px;
      margin: 2px 0;
      position: relative;
      overflow: hidden;
      letter-spacing: 0.3px;
      min-height: 20px; /* 统一最小高度 */
      box-sizing: border-box;
      width: 100%; /* 确保宽度一致 */
      text-decoration: none;
      white-space: nowrap; /* 防止文字换行导致高度不一致 */
      line-height: 1.2; /* 统一行高 */
    }

    .industry-col a::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 0;
      background: linear-gradient(90deg, #4A90E2, #7BB3F0);
      transition: width 0.25s ease;
      z-index: 1;
    }

    .industry-col a span {
      position: relative;
      z-index: 2;
      display: block;
    }

    .industry-col a:hover {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      color: #1A365D;
      transform: translateX(6px); /* 减少移动距离防止抖动 */
      box-shadow:
        0 6px 16px rgba(74,144,226,0.2),
        0 3px 6px rgba(74,144,226,0.1);
      font-weight: 600;
    }

    .industry-col a:hover::before {
      width: 4px;
    }

    .btn-primary {
      background: #ff6a00;
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      text-decoration: none;
      font-weight: 500;
      transition: background 0.2s;
    }

    .btn-primary:hover {
      background: #e55a00;
    }

    body {
      margin: 0;
      font-family: Arial, sans-serif;
    }
  </style>
</head>
<body>
  <nav class="main-navbar">
    <div class="navbar-logo">COMPANY NAME</div>
    
    <!-- 产品菜单 -->
    <div class="nav-dropdown">
      <a href="#" class="nav-link">产品</a>
      <div class="nav-submenu">
        <a href="#">工业同步带</a>
        <a href="#">传动皮带</a>
        <a href="#">输送带</a>
        <a href="#">特殊定制</a>
      </div>
    </div>
    
    <!-- 行业菜单 -->
    <div class="nav-dropdown nav-industries">
      <a href="#" class="nav-link">行业</a>
      <div class="nav-submenu industries-submenu">
        <div class="submenu-header">VIEW ALL INDUSTRIES</div>
        <div class="industries-grid">
          <div class="industry-col">
            <a href="#"><span>ALUMINUM</span></a>
            <a href="#"><span>AUTOMOTIVE</span></a>
            <a href="#"><span>CERAMIC AND GLASS</span></a>
            <a href="#"><span>APPLIANCE</span></a>
            <a href="#"><span>CONSTRUCTION</span></a>
            <a href="#"><span>ELEVATOR</span></a>
          </div>
          <div class="industry-col">
            <a href="#"><span>ENERGY</span></a>
            <a href="#"><span>FITNESS</span></a>
            <a href="#"><span>FOOD</span></a>
            <a href="#"><span>AGRICULTURAL</span></a>
            <a href="#"><span>MACHINE TOOLS</span></a>
            <a href="#"><span>MATERIAL HANDLING</span></a>
          </div>
          <div class="industry-col">
            <a href="#"><span>PACKAGING</span></a>
            <a href="#"><span>MANUFACTURING</span></a>
            <a href="#"><span>MEDICAL INDUSTRY</span></a>
            <a href="#"><span>AUTOMATION</span></a>
            <a href="#"><span>TEXTILE</span></a>
            <a href="#"><span>TOBACCO</span></a>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 信息菜单 -->
    <div class="nav-dropdown">
      <a href="#" class="nav-link">信息</a>
      <div class="nav-submenu">
        <a href="#">公司新闻</a>
        <a href="#">技术资料</a>
        <a href="#">行业动态</a>
        <a href="#">下载中心</a>
      </div>
    </div>
    
    <a href="#" class="btn-primary">Contact Us</a>
  </nav>

  <div style="padding: 50px; background: #f5f5f5; min-height: 500px;">
    <h1>导航栏测试页面</h1>
    <p>请将鼠标悬停在导航栏的"产品"、"行业"或"信息"上测试下拉菜单效果。</p>
    <p>行业菜单应该显示18个行业的三列布局。</p>
  </div>
</body>
</html>
