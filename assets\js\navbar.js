// 导航栏下拉菜单JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 获取导航栏元素
    const navbar = document.querySelector('.main-navbar');
    if (!navbar) return;

    // 滚动效果
    function updateNavbar() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }

    // 监听滚动事件
    window.addEventListener('scroll', updateNavbar, { passive: true });

    // 获取所有下拉菜单项
    const dropdowns = document.querySelectorAll('.nav-dropdown');
    
    dropdowns.forEach(function(dropdown) {
        const submenu = dropdown.querySelector('.nav-submenu');
        
        if (submenu) {
            // 鼠标进入时显示下拉菜单
            dropdown.addEventListener('mouseenter', function() {
                submenu.style.opacity = '1';
                submenu.style.visibility = 'visible';
                submenu.style.transform = 'translateY(0)';
            });
            
            // 鼠标离开时隐藏下拉菜单
            dropdown.addEventListener('mouseleave', function() {
                submenu.style.opacity = '0';
                submenu.style.visibility = 'hidden';
                submenu.style.transform = 'translateY(-10px)';
            });
        }
    });
    
    // 点击页面其他地方时隐藏所有下拉菜单
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.nav-dropdown')) {
            dropdowns.forEach(function(dropdown) {
                const submenu = dropdown.querySelector('.nav-submenu');
                if (submenu) {
                    submenu.style.opacity = '0';
                    submenu.style.visibility = 'hidden';
                    submenu.style.transform = 'translateY(-10px)';
                }
            });
        }
    });
});
