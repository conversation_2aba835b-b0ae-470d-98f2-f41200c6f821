/**
 * 现代化核心JavaScript模块
 * 替换jQuery依赖，使用原生ES6+语法
 * 模块化设计，按需加载
 */

// 核心工具类
class ModernCore {
    constructor() {
        this.modules = new Map();
        this.observers = new Map();
        this.init();
    }

    init() {
        // DOM加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
        } else {
            this.onDOMReady();
        }
    }

    onDOMReady() {
        this.initModules();
        this.setupObservers();
        this.optimizePerformance();
    }

    // 模块注册系统
    registerModule(name, moduleClass) {
        this.modules.set(name, moduleClass);
    }

    // 初始化所有模块
    initModules() {
        this.modules.forEach((ModuleClass, name) => {
            try {
                new ModuleClass();
                console.log(`✅ Module ${name} initialized`);
            } catch (error) {
                console.error(`❌ Failed to initialize module ${name}:`, error);
            }
        });
    }

    // 设置观察者
    setupObservers() {
        // 性能观察者
        if ('PerformanceObserver' in window) {
            const perfObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'largest-contentful-paint') {
                        console.log('LCP:', entry.startTime);
                    }
                }
            });
            perfObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        }

        // 可见性观察者
        if ('IntersectionObserver' in window) {
            const visibilityObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('in-view');
                        this.triggerCustomEvent('elementVisible', { element: entry.target });
                    }
                });
            }, { threshold: 0.1 });

            // 观察所有带有 data-observe 属性的元素
            document.querySelectorAll('[data-observe]').forEach(el => {
                visibilityObserver.observe(el);
            });
        }
    }

    // 性能优化
    optimizePerformance() {
        // 预加载关键资源
        this.preloadCriticalResources();
        
        // 延迟加载非关键脚本
        this.loadNonCriticalScripts();
        
        // 优化图片加载
        this.optimizeImages();
    }

    preloadCriticalResources() {
        const base = (window.themeData && window.themeData.base) ? window.themeData.base : '';
        const criticalResources = [
            base + '/assets/css/style.css',
            base + '/assets/js/contact-box.js'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = resource.endsWith('.css') ? 'style' : 'script';
            link.href = resource;
            document.head.appendChild(link);
        });
    }

    loadNonCriticalScripts() {
        // 延迟加载非关键脚本（若不存在则跳过）
        setTimeout(() => {
            const base = (window.themeData && window.themeData.base) ? window.themeData.base : '';
            const scripts = [
                base + '/assets/js/analytics.js',
                base + '/assets/js/social-sharing.js'
            ];

            scripts.forEach(src => {
                // 仅当可能存在时再尝试加载（简单探测或直接尝试也可，失败不影响主流程）
                const script = document.createElement('script');
                script.src = src;
                script.async = true;
                script.onerror = () => { /* 忽略不可用资源 */ };
                document.head.appendChild(script);
            });
        }, 2000);
    }

    optimizeImages() {
        // 为所有图片添加现代化属性
        document.querySelectorAll('img').forEach(img => {
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }
            if (!img.hasAttribute('decoding')) {
                img.setAttribute('decoding', 'async');
            }
        });
    }

    // 自定义事件系统
    triggerCustomEvent(eventName, detail = {}) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }

    // 工具方法
    static $(selector) {
        return document.querySelector(selector);
    }

    static $$(selector) {
        return document.querySelectorAll(selector);
    }

    static ready(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 动画工具
    static animate(element, keyframes, options = {}) {
        if ('animate' in element) {
            return element.animate(keyframes, {
                duration: 300,
                easing: 'ease-out',
                ...options
            });
        }
        return null;
    }

    // AJAX工具
    static async fetch(url, options = {}) {
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('Fetch error:', error);
            throw error;
        }
    }
}

// 现代化联系表单模块
class ModernContactForm {
    constructor() {
        this.forms = ModernCore.$$('form[data-contact-form]');
        this.init();
    }

    init() {
        this.forms.forEach(form => {
            this.setupForm(form);
        });
    }

    setupForm(form) {
        form.addEventListener('submit', (e) => this.handleSubmit(e));
        
        // 实时验证
        const inputs = form.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', ModernCore.debounce(() => this.validateField(input), 300));
        });
    }

    async handleSubmit(e) {
        e.preventDefault();
        const form = e.target;
        
        if (!this.validateForm(form)) {
            return;
        }

        this.showLoading(form);
        
        try {
            const formData = new FormData(form);
            const response = await ModernCore.fetch('/wp-admin/admin-ajax.php', {
                method: 'POST',
                body: formData
            });

            this.showSuccess(form);
            form.reset();
        } catch (error) {
            this.showError(form, 'Failed to send message. Please try again.');
        } finally {
            this.hideLoading(form);
        }
    }

    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        let isValid = true;
        let message = '';

        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'This field is required';
        } else if (type === 'email' && value && !this.isValidEmail(value)) {
            isValid = false;
            message = 'Please enter a valid email address';
        }

        this.updateFieldStatus(field, isValid, message);
        return isValid;
    }

    validateForm(form) {
        const fields = form.querySelectorAll('input[required], textarea[required]');
        let isValid = true;

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    isValidEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }

    updateFieldStatus(field, isValid, message) {
        const wrapper = field.closest('.form-group') || field.parentElement;
        const errorElement = wrapper.querySelector('.error-message') || this.createErrorElement();
        
        if (!wrapper.querySelector('.error-message')) {
            wrapper.appendChild(errorElement);
        }

        if (isValid) {
            field.classList.remove('error');
            errorElement.textContent = '';
            errorElement.style.display = 'none';
        } else {
            field.classList.add('error');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    createErrorElement() {
        const element = document.createElement('div');
        element.className = 'error-message';
        element.style.cssText = 'color: #e74c3c; font-size: 0.875rem; margin-top: 0.25rem;';
        return element;
    }

    showLoading(form) {
        const button = form.querySelector('button[type="submit"]');
        button.disabled = true;
        button.textContent = 'Sending...';
    }

    hideLoading(form) {
        const button = form.querySelector('button[type="submit"]');
        button.disabled = false;
        button.textContent = 'Send Message';
    }

    showSuccess(form) {
        this.showMessage(form, 'Message sent successfully!', 'success');
    }

    showError(form, message) {
        this.showMessage(form, message, 'error');
    }

    showMessage(form, message, type) {
        const messageElement = document.createElement('div');
        messageElement.className = `form-message ${type}`;
        messageElement.textContent = message;
        messageElement.style.cssText = `
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            background: ${type === 'success' ? '#d4edda' : '#f8d7da'};
            color: ${type === 'success' ? '#155724' : '#721c24'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : '#f5c6cb'};
        `;

        form.insertBefore(messageElement, form.firstChild);

        setTimeout(() => {
            messageElement.remove();
        }, 5000);
    }
}

// 初始化系统
const modernCore = new ModernCore();

// 注册模块
modernCore.registerModule('ContactForm', ModernContactForm);

// 导出全局访问
window.ModernCore = ModernCore;
window.$ = ModernCore.$;
window.$$ = ModernCore.$$;
