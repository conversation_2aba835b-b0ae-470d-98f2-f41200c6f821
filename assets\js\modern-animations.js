/**
 * 现代化动画系统
 * 使用Web Animations API和CSS变量
 * 高性能、可配置的动画解决方案
 */

class ModernAnimations {
    constructor() {
        this.animationQueue = [];
        this.isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        this.init();
    }

    init() {
        this.setupScrollAnimations();
        this.setupHoverAnimations();
        this.setupLoadAnimations();
        this.setupParallaxEffects();
    }

    // 滚动触发动画
    setupScrollAnimations() {
        if ('IntersectionObserver' in window && !this.isReducedMotion) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.triggerScrollAnimation(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            // 观察所有带有动画属性的元素
            document.querySelectorAll('[data-animate]').forEach(el => {
                observer.observe(el);
            });
        }
    }

    triggerScrollAnimation(element) {
        const animationType = element.dataset.animate;
        const delay = parseInt(element.dataset.delay) || 0;
        const duration = parseInt(element.dataset.duration) || 600;

        setTimeout(() => {
            switch (animationType) {
                case 'fadeInUp':
                    this.fadeInUp(element, duration);
                    break;
                case 'fadeInLeft':
                    this.fadeInLeft(element, duration);
                    break;
                case 'fadeInRight':
                    this.fadeInRight(element, duration);
                    break;
                case 'scaleIn':
                    this.scaleIn(element, duration);
                    break;
                case 'slideInUp':
                    this.slideInUp(element, duration);
                    break;
                default:
                    this.fadeIn(element, duration);
            }
        }, delay);
    }

    // 动画方法
    fadeIn(element, duration = 600) {
        element.style.opacity = '0';
        element.style.visibility = 'visible';
        
        if (element.animate) {
            element.animate([
                { opacity: 0 },
                { opacity: 1 }
            ], {
                duration,
                easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
                fill: 'forwards'
            });
        } else {
            element.style.transition = `opacity ${duration}ms ease-out`;
            element.style.opacity = '1';
        }
    }

    fadeInUp(element, duration = 600) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.visibility = 'visible';
        
        if (element.animate) {
            element.animate([
                { opacity: 0, transform: 'translateY(30px)' },
                { opacity: 1, transform: 'translateY(0)' }
            ], {
                duration,
                easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
                fill: 'forwards'
            });
        } else {
            element.style.transition = `all ${duration}ms ease-out`;
            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, 10);
        }
    }

    fadeInLeft(element, duration = 600) {
        element.style.opacity = '0';
        element.style.transform = 'translateX(-30px)';
        element.style.visibility = 'visible';
        
        if (element.animate) {
            element.animate([
                { opacity: 0, transform: 'translateX(-30px)' },
                { opacity: 1, transform: 'translateX(0)' }
            ], {
                duration,
                easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
                fill: 'forwards'
            });
        }
    }

    fadeInRight(element, duration = 600) {
        element.style.opacity = '0';
        element.style.transform = 'translateX(30px)';
        element.style.visibility = 'visible';
        
        if (element.animate) {
            element.animate([
                { opacity: 0, transform: 'translateX(30px)' },
                { opacity: 1, transform: 'translateX(0)' }
            ], {
                duration,
                easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
                fill: 'forwards'
            });
        }
    }

    scaleIn(element, duration = 600) {
        element.style.opacity = '0';
        element.style.transform = 'scale(0.8)';
        element.style.visibility = 'visible';
        
        if (element.animate) {
            element.animate([
                { opacity: 0, transform: 'scale(0.8)' },
                { opacity: 1, transform: 'scale(1)' }
            ], {
                duration,
                easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
                fill: 'forwards'
            });
        }
    }

    slideInUp(element, duration = 600) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(100%)';
        element.style.visibility = 'visible';
        
        if (element.animate) {
            element.animate([
                { opacity: 0, transform: 'translateY(100%)' },
                { opacity: 1, transform: 'translateY(0)' }
            ], {
                duration,
                easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
                fill: 'forwards'
            });
        }
    }

    // 悬停动画
    setupHoverAnimations() {
        document.querySelectorAll('[data-hover-animate]').forEach(element => {
            const animationType = element.dataset.hoverAnimate;
            
            element.addEventListener('mouseenter', () => {
                this.triggerHoverAnimation(element, animationType, true);
            });
            
            element.addEventListener('mouseleave', () => {
                this.triggerHoverAnimation(element, animationType, false);
            });
        });
    }

    triggerHoverAnimation(element, type, isEnter) {
        if (this.isReducedMotion) return;

        switch (type) {
            case 'lift':
                this.liftAnimation(element, isEnter);
                break;
            case 'glow':
                this.glowAnimation(element, isEnter);
                break;
            case 'rotate':
                this.rotateAnimation(element, isEnter);
                break;
            case 'scale':
                this.scaleAnimation(element, isEnter);
                break;
        }
    }

    liftAnimation(element, isEnter) {
        const transform = isEnter ? 'translateY(-8px) scale(1.02)' : 'translateY(0) scale(1)';
        const boxShadow = isEnter ? 
            '0 10px 25px rgba(0,0,0,0.15), 0 5px 10px rgba(0,0,0,0.1)' : 
            '0 2px 8px rgba(0,0,0,0.1)';

        if (element.animate) {
            element.animate([
                { transform: element.style.transform || 'translateY(0) scale(1)' },
                { transform }
            ], {
                duration: 300,
                easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
                fill: 'forwards'
            });
        }
        
        element.style.boxShadow = boxShadow;
        element.style.transition = 'box-shadow 0.3s ease';
    }

    glowAnimation(element, isEnter) {
        const boxShadow = isEnter ? 
            '0 0 20px rgba(255, 106, 0, 0.3), 0 0 40px rgba(255, 106, 0, 0.1)' : 
            'none';
        
        element.style.boxShadow = boxShadow;
        element.style.transition = 'box-shadow 0.3s ease';
    }

    // {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
    // Page load animations
    setupLoadAnimations() {
        window.addEventListener('load', () => {
            this.triggerLoadAnimations();
        });
    }

    triggerLoadAnimations() {
        // {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
        // Add loading animations for hero section
        const heroElements = document.querySelectorAll('.hero-section [data-load-animate]');
        heroElements.forEach((element, index) => {
            setTimeout(() => {
                this.fadeInUp(element, 800);
            }, index * 200);
        });

        // {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
        // Add animations for navigation
        const navItems = document.querySelectorAll('.nav-link');
        navItems.forEach((item, index) => {
            setTimeout(() => {
                this.fadeInUp(item, 400);
            }, 100 + index * 50);
        });
    }

    // 视差效果
    setupParallaxEffects() {
        if (this.isReducedMotion) return;

        const parallaxElements = document.querySelectorAll('[data-parallax]');
        if (parallaxElements.length === 0) return;

        const handleScroll = ModernCore.throttle(() => {
            const scrollY = window.pageYOffset;
            
            parallaxElements.forEach(element => {
                const speed = parseFloat(element.dataset.parallax) || 0.5;
                const yPos = -(scrollY * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
        }, 16); // 60fps

        window.addEventListener('scroll', handleScroll, { passive: true });
    }

    // 数字计数动画
    animateCounter(element, start = 0, end, duration = 2000) {
        const startTime = performance.now();
        const startValue = start;
        const endValue = end;

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用easeOutQuart缓动函数
            const easeProgress = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.floor(startValue + (endValue - startValue) * easeProgress);
            
            element.textContent = currentValue.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    // {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
    // Typewriter effect
    typeWriter(element, text, speed = 50) {
        element.textContent = '';
        let i = 0;
        
        const type = () => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        };
        
        type();
    }

    // 波纹效果
    createRipple(element, event) {
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        const ripple = document.createElement('span');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            pointer-events: none;
        `;
        
        element.appendChild(ripple);
        
        if (ripple.animate) {
            ripple.animate([
                { transform: 'scale(0)', opacity: 1 },
                { transform: 'scale(1)', opacity: 0 }
            ], {
                duration: 600,
                easing: 'ease-out'
            }).onfinish = () => ripple.remove();
        } else {
            setTimeout(() => ripple.remove(), 600);
        }
    }
}

// {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
// Initialize animation system
document.addEventListener('DOMContentLoaded', () => {
    window.modernAnimations = new ModernAnimations();
});

// {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
// Add ripple effect for buttons
document.addEventListener('click', (e) => {
    if (e.target.matches('.btn-primary, .ripple-effect')) {
        window.modernAnimations?.createRipple(e.target, e);
    }
});

// {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
// Export
window.ModernAnimations = ModernAnimations;
