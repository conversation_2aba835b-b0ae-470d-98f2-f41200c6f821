<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Energy Industry - Test Page</title>
  <style>
    body {
      margin: 0;
      font-family: 'SF Pro Display', 'Segoe UI', Arial, sans-serif;
      font-size: 17px;
      line-height: 1.7;
      color: #1a2a3a;
      background: #f4f6fa;
    }

    .container {
      width: 90%;
      max-width: 1200px;
      margin: 0 auto;
    }

    /* Hero Section */
    .industry-hero {
      background: linear-gradient(135deg, #1A365D 0%, #2E5C8A 50%, #4A90E2 100%);
      color: white;
      padding: 80px 0 60px;
      position: relative;
      overflow: hidden;
    }

    .industry-hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: 
        radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
      pointer-events: none;
    }

    .industry-hero .container {
      display: flex;
      align-items: center;
      gap: 60px;
      position: relative;
      z-index: 2;
    }

    .industry-hero-content {
      flex: 1;
    }

    .industry-title {
      font-size: 3.5rem;
      font-weight: 700;
      margin-bottom: 20px;
      line-height: 1.2;
    }

    .industry-subtitle {
      font-size: 1.3rem;
      opacity: 0.9;
      line-height: 1.6;
      margin-bottom: 30px;
    }

    .hero-features {
      display: flex;
      gap: 30px;
      margin-top: 30px;
      flex-wrap: wrap;
    }

    .feature-item {
      display: flex;
      align-items: center;
      gap: 10px;
      background: rgba(76,175,80,0.1);
      border: 1px solid rgba(76,175,80,0.3);
      padding: 12px 20px;
      border-radius: 25px;
      backdrop-filter: blur(10px);
    }

    .feature-icon {
      font-size: 1.2rem;
    }

    /* Energy Sectors */
    .energy-sectors {
      padding: 80px 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
    }

    .energy-sectors h2 {
      font-size: 2.5rem;
      color: #1A365D;
      margin-bottom: 50px;
      text-align: center;
    }

    .sectors-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 30px;
    }

    .sector-card {
      background: white;
      border-radius: 16px;
      padding: 30px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .sector-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #4CAF50, #8BC34A);
    }

    .sector-card.traditional::before {
      background: linear-gradient(90deg, #FF9800, #FFC107);
    }

    .sector-card.oil-gas::before {
      background: linear-gradient(90deg, #2196F3, #03A9F4);
    }

    .sector-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 16px 48px rgba(0,0,0,0.15);
    }

    .sector-icon {
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
    }

    .sector-card h3 {
      font-size: 1.4rem;
      color: #1A365D;
      margin-bottom: 15px;
      text-align: center;
    }

    .sector-card p {
      color: #666;
      line-height: 1.6;
      margin-bottom: 20px;
    }

    .sector-applications {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .sector-applications li {
      padding: 8px 0;
      padding-left: 20px;
      position: relative;
      color: #555;
      font-size: 0.9rem;
    }

    .sector-applications li::before {
      content: '⚡';
      position: absolute;
      left: 0;
      color: #4CAF50;
    }

    .sector-card.traditional .sector-applications li::before {
      color: #FF9800;
    }

    .sector-card.oil-gas .sector-applications li::before {
      color: #2196F3;
    }

    /* Application Showcase */
    .application-showcase {
      padding: 80px 0;
      background: white;
    }

    .application-showcase h2 {
      font-size: 2.5rem;
      color: #1A365D;
      margin-bottom: 50px;
      text-align: center;
    }

    .showcase-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 40px;
    }

    .showcase-item {
      display: flex;
      gap: 25px;
      background: #f8f9fa;
      border-radius: 16px;
      padding: 30px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.08);
      transition: transform 0.3s ease;
    }

    .showcase-item:hover {
      transform: translateY(-5px);
    }

    .showcase-image {
      flex: 0 0 120px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .image-placeholder {
      width: 100px;
      height: 100px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    }

    .image-placeholder.wind-turbine {
      background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    }

    .image-placeholder.solar-farm {
      background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    }

    .showcase-content {
      flex: 1;
    }

    .showcase-content h3 {
      color: #1A365D;
      margin-bottom: 15px;
      font-size: 1.3rem;
    }

    .showcase-content p {
      color: #666;
      line-height: 1.6;
      margin-bottom: 10px;
    }

    .showcase-specs {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
      margin-top: 15px;
    }

    .showcase-specs span {
      background: #e3f2fd;
      color: #1A365D;
      padding: 6px 12px;
      border-radius: 12px;
      font-size: 0.85rem;
      font-weight: 500;
    }

    @media (max-width: 768px) {
      .industry-hero .container {
        flex-direction: column;
        text-align: center;
      }
      
      .industry-title {
        font-size: 2.5rem;
      }
      
      .hero-features {
        flex-direction: column;
        gap: 15px;
        align-items: center;
      }
      
      .feature-item {
        width: 100%;
        max-width: 250px;
        justify-content: center;
      }
      
      .sectors-grid,
      .showcase-grid {
        grid-template-columns: 1fr;
      }
      
      .showcase-item {
        flex-direction: column;
        text-align: center;
      }
      
      .showcase-image {
        flex: none;
        margin-bottom: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="industry-page energy-industry">
    <!-- Hero Section -->
    <section class="industry-hero">
      <div class="container">
        <div class="industry-hero-content">
          <h1 class="industry-title">ENERGY INDUSTRY</h1>
          <p class="industry-subtitle">Reliable power transmission solutions for renewable energy, oil & gas, and power generation applications</p>
          <div class="hero-features">
            <div class="feature-item">
              <span class="feature-icon">🌱</span>
              <span>Renewable Energy</span>
            </div>
            <div class="feature-item">
              <span class="feature-icon">⚡</span>
              <span>Power Generation</span>
            </div>
            <div class="feature-item">
              <span class="feature-icon">🛢️</span>
              <span>Oil & Gas</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Energy Sectors -->
    <section class="energy-sectors">
      <div class="container">
        <h2>Energy Sectors We Serve</h2>
        <div class="sectors-grid">
          <div class="sector-card renewable">
            <div class="sector-icon">
              <svg width="60" height="60" viewBox="0 0 60 60" fill="none">
                <circle cx="30" cy="30" r="25" stroke="#4CAF50" stroke-width="2" fill="rgba(76,175,80,0.1)"/>
                <path d="M30 10L35 25L30 30L25 25L30 10Z" fill="#4CAF50"/>
                <path d="M50 30L35 35L30 30L35 25L50 30Z" fill="#4CAF50"/>
                <path d="M30 50L25 35L30 30L35 35L30 50Z" fill="#4CAF50"/>
                <path d="M10 30L25 25L30 30L25 35L10 30Z" fill="#4CAF50"/>
              </svg>
            </div>
            <h3>Renewable Energy</h3>
            <p>Wind turbines, solar tracking systems, hydroelectric generators, and biomass processing equipment.</p>
            <ul class="sector-applications">
              <li>Wind turbine gearboxes</li>
              <li>Solar panel tracking systems</li>
              <li>Hydroelectric turbines</li>
              <li>Biomass conveyors</li>
            </ul>
          </div>

          <div class="sector-card traditional">
            <div class="sector-icon">
              <svg width="60" height="60" viewBox="0 0 60 60" fill="none">
                <rect x="15" y="20" width="30" height="25" rx="2" stroke="#FF9800" stroke-width="2" fill="rgba(255,152,0,0.1)"/>
                <path d="M20 25h20M20 30h20M20 35h15" stroke="#FF9800" stroke-width="2"/>
                <path d="M25 15L30 20L35 15" stroke="#FF9800" stroke-width="2"/>
                <path d="M25 45L30 50L35 45" stroke="#FF9800" stroke-width="2"/>
              </svg>
            </div>
            <h3>Traditional Power</h3>
            <p>Coal, natural gas, and nuclear power plants requiring robust, reliable transmission solutions.</p>
            <ul class="sector-applications">
              <li>Steam turbine systems</li>
              <li>Coal handling equipment</li>
              <li>Cooling tower fans</li>
              <li>Generator drives</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Application Showcase -->
    <section class="application-showcase">
      <div class="container">
        <h2>Real-World Energy Applications</h2>
        <div class="showcase-grid">
          <div class="showcase-item">
            <div class="showcase-image">
              <div class="image-placeholder wind-turbine">
                <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
                  <circle cx="40" cy="40" r="35" stroke="#4CAF50" stroke-width="2"/>
                  <path d="M40 15L45 35L40 40L35 35L40 15Z" fill="#4CAF50"/>
                  <path d="M65 40L45 45L40 40L45 35L65 40Z" fill="#4CAF50"/>
                  <path d="M40 65L35 45L40 40L45 45L40 65Z" fill="#4CAF50"/>
                </svg>
              </div>
            </div>
            <div class="showcase-content">
              <h3>Offshore Wind Farm</h3>
              <p><strong>Challenge:</strong> 3MW offshore wind turbines requiring maintenance-free operation in salt spray environment.</p>
              <p><strong>Solution:</strong> Custom polyurethane timing belts with enhanced UV and corrosion resistance, achieving 10+ year service life.</p>
              <div class="showcase-specs">
                <span>Power: 3MW</span>
                <span>Environment: Offshore</span>
                <span>Service Life: 10+ years</span>
              </div>
            </div>
          </div>

          <div class="showcase-item">
            <div class="showcase-image">
              <div class="image-placeholder solar-farm">
                <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
                  <rect x="20" y="30" width="40" height="25" rx="2" stroke="#FF9800" stroke-width="2" fill="rgba(255,152,0,0.1)"/>
                  <path d="M25 35h30M25 40h30M25 45h25" stroke="#FF9800" stroke-width="2"/>
                  <path d="M30 20L40 30L50 20" stroke="#FF9800" stroke-width="2"/>
                </svg>
              </div>
            </div>
            <div class="showcase-content">
              <h3>Desert Solar Tracking</h3>
              <p><strong>Challenge:</strong> 100MW solar farm in extreme desert conditions with temperature swings from -20°C to +60°C.</p>
              <p><strong>Solution:</strong> EPDM-based timing belts with specialized UV stabilizers and sand-resistant edge sealing.</p>
              <div class="showcase-specs">
                <span>Capacity: 100MW</span>
                <span>Temperature: -20°C to +60°C</span>
                <span>Tracking Accuracy: ±0.1°</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</body>
</html>
