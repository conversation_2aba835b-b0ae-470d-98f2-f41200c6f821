<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Packaging Industry - Hover Effect Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
            padding: 40px 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            color: #1A365D;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 50px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* 解决方案网格 */
        .solutions-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin-top: 40px;
        }

        /* 卡片基础样式 */
        .solution-category {
            background: white;
            border-radius: 16px;
            padding: 0;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            display: flex;
            flex-direction: column;
            height: 100%;
            position: relative;
            overflow: hidden;
            min-height: 400px;
            max-height: 400px;
            will-change: transform;
            backface-visibility: hidden;
        }

        .solution-category:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.18);
        }

        /* 卡片头部 */
        .category-header {
            background: linear-gradient(135deg, #9C27B0 0%, #E1BEE7 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
            min-height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .category-header h3 {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .rating-badge {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        /* 卡片内容区域 */
        .category-content {
            padding: 25px;
            display: flex;
            flex-direction: column;
            flex: 1;
            position: relative;
        }

        /* 产品特性区域 */
        .product-features {
            flex: 1;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .product-features ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .product-features li {
            padding: 8px 0;
            padding-left: 20px;
            position: relative;
            color: #555;
            line-height: 1.4;
            transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            opacity: 1;
        }

        .product-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #9C27B0;
            font-weight: bold;
            font-size: 1.1em;
        }

        /* 默认状态：隐藏第4、5个特性 */
        .product-features li:nth-child(n+4) {
            opacity: 0.4;
            font-size: 0.9em;
        }

        /* 展开指示器 */
        .product-features::after {
            content: '···';
            position: absolute;
            bottom: 0;
            right: 0;
            background: linear-gradient(90deg, transparent, white 30%);
            padding: 0 15px 0 30px;
            color: #9C27B0;
            font-weight: bold;
            font-size: 1.2em;
            line-height: 1;
            transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* 应用场景区域 */
        .applications {
            margin-top: auto;
            padding-top: 20px;
            background: rgba(156, 39, 176, 0.05);
            margin: 0 -25px -25px -25px;
            padding: 20px 25px 25px 25px;
            border-radius: 0 0 16px 16px;
            border-top: 2px solid #f0f0f0;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            opacity: 1;
            transform: translateY(0);
        }

        .applications h4 {
            color: #9C27B0;
            margin-bottom: 10px;
            font-size: 1rem;
            font-weight: 600;
        }

        .applications p {
            color: #666;
            font-style: italic;
            line-height: 1.5;
            position: relative;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            min-height: 4.5em;
        }

        /* 悬停状态：压缩Applications，展示完整特性 */
        .solution-category:hover .applications {
            padding: 10px 25px 15px 25px;
            margin: 0 -25px -25px -25px;
            background: rgba(156, 39, 176, 0.08);
            border-top: 1px solid rgba(156, 39, 176, 0.2);
        }

        .solution-category:hover .applications h4 {
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .solution-category:hover .applications p {
            font-size: 0.85rem;
            line-height: 1.3;
            -webkit-line-clamp: 2;
            min-height: 2.6em;
        }

        .solution-category:hover .product-features {
            margin-bottom: 15px;
        }

        .solution-category:hover .product-features li:nth-child(n+4) {
            opacity: 1;
            font-size: 1em;
            transition-delay: 0.2s;
        }

        .solution-category:hover .product-features::after {
            opacity: 0;
            transform: translateY(5px);
        }

        /* 悬停时的微妙视觉增强 */
        .solution-category:hover .category-header {
            transform: translateY(-1px);
            transition: transform 0.3s ease;
        }

        .solution-category:hover .product-features li::before {
            color: #7B1FA2;
            transition: color 0.3s ease;
        }

        /* 特殊颜色主题 */
        .high-speed .category-header {
            background: linear-gradient(135deg, #FF5722 0%, #FF8A65 100%);
        }

        .food-grade .category-header {
            background: linear-gradient(135deg, #4CAF50 0%, #81C784 100%);
        }

        .precision .category-header {
            background: linear-gradient(135deg, #2196F3 0%, #64B5F6 100%);
        }

        .cleanroom .category-header {
            background: linear-gradient(135deg, #9C27B0 0%, #BA68C8 100%);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .solutions-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }
        }

        @media (max-width: 768px) {
            .solutions-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .container {
                padding: 20px 15px;
            }
        }

        /* 提示文字 */
        .hover-hint {
            text-align: center;
            margin-top: 30px;
            padding: 15px;
            background: rgba(156, 39, 176, 0.1);
            border-radius: 8px;
            color: #7B1FA2;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="section-title">Packaging Solutions</h2>
        <p class="section-subtitle">Hover over cards to see complete features list - Applications section will compress to make space</p>
        
        <div class="solutions-grid">
            <div class="solution-category high-speed">
                <div class="category-header">
                    <h3>High-Speed Timing Belts</h3>
                    <span class="rating-badge">Up to 50 m/s</span>
                </div>
                <div class="category-content">
                    <div class="product-features">
                        <ul>
                            <li>Low stretch polyurethane construction</li>
                            <li>Precision-ground tooth profile</li>
                            <li>Dynamic balancing for smooth operation</li>
                            <li>Reduced vibration and noise</li>
                            <li>Extended fatigue life</li>
                        </ul>
                    </div>
                    <div class="applications">
                        <h4>Applications:</h4>
                        <p>High-speed filling machines, labeling equipment, cartoning systems, flow wrappers</p>
                    </div>
                </div>
            </div>

            <div class="solution-category food-grade">
                <div class="category-header">
                    <h3>Food-Grade Belts</h3>
                    <span class="rating-badge">FDA Compliant</span>
                </div>
                <div class="category-content">
                    <div class="product-features">
                        <ul>
                            <li>FDA 21 CFR 177.2600 approved materials</li>
                            <li>Smooth, non-porous surface</li>
                            <li>Resistant to oils and cleaning agents</li>
                            <li>Blue color for contamination detection</li>
                            <li>Temperature range: -20°C to +80°C</li>
                        </ul>
                    </div>
                    <div class="applications">
                        <h4>Applications:</h4>
                        <p>Food processing lines, beverage bottling, dairy packaging, meat processing</p>
                    </div>
                </div>
            </div>

            <div class="solution-category precision">
                <div class="category-header">
                    <h3>Precision Positioning Belts</h3>
                    <span class="rating-badge">±0.05mm Accuracy</span>
                </div>
                <div class="category-content">
                    <div class="product-features">
                        <ul>
                            <li>Ultra-precise tooth geometry</li>
                            <li>Minimal backlash design</li>
                            <li>Superior dimensional stability</li>
                            <li>Consistent tracking performance</li>
                            <li>Low coefficient of friction</li>
                        </ul>
                    </div>
                    <div class="applications">
                        <h4>Applications:</h4>
                        <p>Pick-and-place systems, robotic packaging, vision inspection, quality control</p>
                    </div>
                </div>
            </div>

            <div class="solution-category cleanroom">
                <div class="category-header">
                    <h3>Cleanroom Belts</h3>
                    <span class="rating-badge">ISO Class 5</span>
                </div>
                <div class="category-content">
                    <div class="product-features">
                        <ul>
                            <li>Low particle generation materials</li>
                            <li>Antistatic properties available</li>
                            <li>Chemical resistant compounds</li>
                            <li>Smooth edge finish</li>
                            <li>Validation documentation provided</li>
                        </ul>
                    </div>
                    <div class="applications">
                        <h4>Applications:</h4>
                        <p>Pharmaceutical packaging, medical device assembly, semiconductor packaging</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="hover-hint">
            💡 Hover over any card to see the complete features list. The Applications section will compress to make space while remaining visible.
        </div>
    </div>
</body>
</html>
